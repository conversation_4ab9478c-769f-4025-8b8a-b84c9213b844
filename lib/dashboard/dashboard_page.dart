// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mialamobile/dashboard/add_delivery.dart';
import 'package:mialamobile/dashboard/home/<USER>';
import 'package:mialamobile/dashboard/services/service_tab.dart';
import 'package:mialamobile/dashboard/history/transaction_history.dart';
import 'package:mialamobile/dashboard/profile/profile_tab.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  int _selectedIndex = 0;

  // Pages to display when bottom navigation items are tapped
  final List<Widget> _pages = [
    const HomeTab(),
    const ServicesTab(),
    const TransactionHistory(),
    const ProfileTab(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff121212),
      body: _pages[_selectedIndex],
      floatingActionButton: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddDelivery(),
            ),
          );
        },
        child: SvgPicture.asset(
          'assets/icons/add_red.svg',
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: BottomAppBar(
        notchMargin: 10,
        shape: const CircularNotchedRectangle(),
        color: const Color(0xff1E1E1E),
        child: Container(
          height: 60,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Home icon
              MaterialButton(
                minWidth: 40,
                onPressed: () {
                  _onItemTapped(0);
                },
                child: SvgPicture.asset(
                  'assets/icons/home.svg',
                  color: _selectedIndex == 0
                      ? const Color(0xffB10303)
                      : const Color(0xff8C8C8C),
                  width: 24,
                  height: 24,
                ),
              ),
              // Package icon
              MaterialButton(
                minWidth: 40,
                onPressed: () {
                  _onItemTapped(1);
                },
                child: SvgPicture.asset(
                  'assets/icons/package.svg',
                  color: _selectedIndex == 1
                      ? const Color(0xffB10303)
                      : const Color(0xff8C8C8C),
                  width: 24,
                  height: 24,
                ),
              ),
              // Receipt icon
              MaterialButton(
                minWidth: 40,
                onPressed: () {
                  _onItemTapped(2);
                },
                child: SvgPicture.asset(
                  'assets/icons/receipt.svg',
                  color: _selectedIndex == 2
                      ? const Color(0xffB10303)
                      : const Color(0xff8C8C8C),
                  width: 24,
                  height: 24,
                ),
              ),
              // Profile icon
              MaterialButton(
                minWidth: 40,
                onPressed: () {
                  _onItemTapped(3);
                },
                child: SvgPicture.asset(
                  'assets/icons/profile.svg',
                  color: _selectedIndex == 3
                      ? const Color(0xffB10303)
                      : const Color(0xff8C8C8C),
                  width: 24,
                  height: 24,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
