import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/search_input.dart';
import 'package:mialamobile/dashboard/home/<USER>/notification_box.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  TextEditingController searchController = TextEditingController();
  List<Map<String, dynamic>> notifications = [
    {
      'iconPath': 'assets/icons/order.svg',
      'title': 'New Order Assigned: #ORD1452',
      'subtitle': 'You have been assigned a new delivery. Check details now.',
      'time': '08:00am',
    },
    {
      'iconPath': 'assets/icons/star_check.svg',
      'title': 'Payment Received: ₦5,000',
      'subtitle': 'A payment has been confirmed for Order #ORD1398',
      'time': '08:00am',
    },
    {
      'iconPath': 'assets/icons/failed_circle.svg',
      'title': 'Failed Delivery Reported: #ORD1332',
      'subtitle':
          'The delivery for Order #ORD1332 failed. Please update the status.',
      'time': '08:00am',
    },
    {
      'iconPath': 'assets/icons/check_circle.svg',
      'title': 'Payout Processed: ₦25,000',
      'subtitle': 'our latest payout has been processed and will reflect soon',
      'time': '08:00am',
    },
    {
      'iconPath': 'assets/icons/pending.svg',
      'title': 'Reminder: Update Your Deliveries',
      'subtitle': 'You have pending deliveries that need to be updated',
      'time': '08:00am',
    },
    {
      'iconPath': 'assets/icons/message.svg',
      'title': 'Admin Messages & Announcements',
      'subtitle':
          'Please review the updated delivery policies effective from next month',
      'time': '08:00am',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xff121212),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(
                  left: 16, right: 16, bottom: 32, top: 16),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: SvgPicture.asset('assets/icons/cancel.svg'),
                    ),
                    Text(
                      'Notifications',
                      style: GoogleFonts.poppins(
                          textStyle: const TextStyle(
                              color: Color(0xffFFFFFF),
                              fontSize: 14,
                              fontWeight: FontWeight.w500)),
                    ),
                    PopupMenuButton<String>(
                      color: const Color(0xff1E1E1E),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                        side: const BorderSide(
                            color: Color(0xff1E1E1E)), // Added border
                      ),
                      icon: SvgPicture.asset('assets/icons/more_vertical.svg'),
                      itemBuilder: (BuildContext context) =>
                          <PopupMenuEntry<String>>[
                        PopupMenuItem<String>(
                          value: 'mark_all_read',
                          height: 37.0, // Added height
                          child: Center(
                            child: Text(
                              'Mark all as read',
                              style: GoogleFonts.poppins(
                                textStyle: const TextStyle(
                                  color: Color(0xffFFFFFF),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                      onSelected: (String value) {
                        if (value == 'mark_all_read') {
                          // Add logic to mark all notifications as read
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'All notifications marked as read',
                                style: GoogleFonts.poppins(),
                              ),
                              backgroundColor: const Color(0xffB10303),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
                const Gap(23),
                // Search input with both prefix and suffix icons
                SearchInput(
                  focusedBorderColor: Colors.transparent,
                  hintText: '',
                  controller: searchController,
                  prefixIcon: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: SvgPicture.asset(
                      'assets/icons/preference.svg',
                    ),
                  ),
                  suffixIcon: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: SvgPicture.asset(
                      'assets/icons/search.svg',
                    ),
                  ),
                  onChanged: (value) {
                    // Handle search functionality
                    setState(() {
                      // Filter notifications based on search text
                    });
                  },
                ),
                const Gap(24),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text('Today',
                      style: GoogleFonts.poppins(
                          textStyle: const TextStyle(
                              color: Color(0xffFFFFFF),
                              fontSize: 14,
                              fontWeight: FontWeight.w500))),
                ),
                const Gap(15),
                // Notification list
                ListView.builder(
                  shrinkWrap: true,
                  itemCount: notifications
                      .length, // Assuming notifications is a list of objects
                  itemBuilder: (context, index) {
                    final notification = notifications[index];
                    return NotificationBox(
                      iconPath: notification['iconPath'],
                      title: notification['title'],
                      subtitle: notification['subtitle'],
                      time: notification['time'],
                    );
                  },
                ),
              ]),
            ),
          ),
        ));
  }
}
