import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mialamobile/components/search_input.dart';
import 'package:mialamobile/components/text_dropdown.dart';
import 'package:mialamobile/dashboard/home/<USER>/recent_delivery.dart';

class ServicesTab extends StatefulWidget {
  const ServicesTab({super.key});

  @override
  State<ServicesTab> createState() => _ServicesTabState();
}

class _ServicesTabState extends State<ServicesTab> {
  final List<Map<String, dynamic>> _listDeliveries = [
    {
      'productName': 'iPhone 15 Pro max(1)',
      'trackingId': 'DCV4387900',
      'deliveryAddress': '14th brown street, Ajah Lagos State',
      'deliveryDate': '15Mar',
      'deliveryTime': '2:35am',
      'deliveryStatus': 'Delivered',
      'statusColor': const Color(0xff153D80),
    },
    {
      'productName': 'MacBook Pro M2',
      'trackingId': 'DCV4387901',
      'deliveryAddress': '25 Marina Road, Victoria Island, Lagos',
      'deliveryDate': '14Mar',
      'deliveryTime': '4:20pm',
      'deliveryStatus': 'Delivered',
      'statusColor': const Color(0xff153D80),
    },
    {
      'productName': 'AirPods Pro 2',
      'trackingId': 'DCV4387902',
      'deliveryAddress': '7 Admiralty Way, Lekki Phase 1, Lagos',
      'deliveryDate': '13Mar',
      'deliveryTime': '11:45am',
      'deliveryStatus': 'Delivered',
      'statusColor': const Color(0xff153D80),
    },
    {
      'productName': 'iPhone 15 Pro max(1)',
      'trackingId': 'DCV4387900',
      'deliveryAddress': '14th brown street, Ajah Lagos State',
      'deliveryDate': '15Mar',
      'deliveryTime': '2:35am',
      'deliveryStatus': 'Delivered',
      'statusColor': const Color(0xff153D80),
    },
    {
      'productName': 'MacBook Pro M2',
      'trackingId': 'DCV4387901',
      'deliveryAddress': '25 Marina Road, Victoria Island, Lagos',
      'deliveryDate': '14Mar',
      'deliveryTime': '4:20pm',
      'deliveryStatus': 'Delivered',
      'statusColor': const Color(0xff153D80),
    },
    {
      'productName': 'AirPods Pro 2',
      'trackingId': 'DCV4387902',
      'deliveryAddress': '7 Admiralty Way, Lekki Phase 1, Lagos',
      'deliveryDate': '13Mar',
      'deliveryTime': '11:45am',
      'deliveryStatus': 'Delivered',
      'statusColor': const Color(0xff153D80),
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
          child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Delivered Products',
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
              const Gap(20),
              SearchInput(
                hintText: '',
                suffixIcon: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: SvgPicture.asset(
                    'assets/icons/search.svg',
                  ),
                ),
                focusedBorderColor: Colors.transparent,
              ),
              const Gap(5),
              TextDropdown(
                value: 'Jan',
                items: const ['Jan', 'Feb', 'Mar', 'Apr'],
                onChanged: (newValue) {},
                dropdownColor:
                    const Color(0xff121212), // Dark background from memory
                dropdownWidth: 120,
                selectedItemColor: const Color(0xffB10303), // Primary red
                itemColor: Colors.white,
              ),
              const Gap(5),
              ..._listDeliveries.map((delivery) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: RecentDelivery(
                    productName: delivery['productName'],
                    trackingId: delivery['trackingId'],
                    deliveryAddress: delivery['deliveryAddress'],
                    deliveryDate: delivery['deliveryDate'],
                    deliveryTime: delivery['deliveryTime'],
                    deliveryStatus: delivery['deliveryStatus'],
                    statusColor: delivery['statusColor'],
                  ),
                );
              }),
            ],
          ),
        ),
      )),
    );
  }
}
