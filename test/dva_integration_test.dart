import 'package:flutter_test/flutter_test.dart';
import 'package:mialamobile/models/dva_info.dart';
import 'package:mialamobile/api/endpoints.dart';

void main() {
  group('DVA Integration Tests', () {
    test('DvaInfo model should parse JSON correctly', () {
      // Test data based on the Postman response
      final testData = {
        'accountName': 'MIALA/EVELYN',
        'accountNumber': '**********',
        'bankName': 'Wema Bank',
        'balance': 0.0,
      };

      final dvaInfo = DvaInfo.fromJson(testData);

      expect(dvaInfo.accountName, equals('MIALA/EVELYN'));
      expect(dvaInfo.accountNumber, equals('**********'));
      expect(dvaInfo.bankName, equals('Wema Bank'));
      expect(dvaInfo.balance, equals(0.0));
    });

    test('DvaInfo model should handle missing fields gracefully', () {
      final testData = <String, dynamic>{};

      final dvaInfo = DvaInfo.fromJson(testData);

      expect(dvaInfo.accountName, equals(''));
      expect(dvaInfo.accountNumber, equals(''));
      expect(dvaInfo.bankName, equals(''));
      expect(dvaInfo.balance, equals(0.0));
    });

    test('DVA endpoint should generate correct URL', () {
      const testEmail = '<EMAIL>';
      final expectedUrl = 'https://miala.onrender.com/api/v1/rider/<EMAIL>/dva-info';
      
      final actualUrl = ApiEndpoints.dvaInfo(testEmail);
      
      expect(actualUrl, equals(expectedUrl));
    });

    test('DvaInfo toJson should work correctly', () {
      final dvaInfo = DvaInfo(
        accountName: 'MIALA/EVELYN',
        accountNumber: '**********',
        bankName: 'Wema Bank',
        balance: 0.0,
      );

      final json = dvaInfo.toJson();

      expect(json['accountName'], equals('MIALA/EVELYN'));
      expect(json['accountNumber'], equals('**********'));
      expect(json['bankName'], equals('Wema Bank'));
      expect(json['balance'], equals(0.0));
    });
  });
}
