// Mocks generated by Mockito 5.4.6 from annotations
// in mialamobile/test/onboarding/register/register_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:ui' as _i7;

import 'package:mialamobile/api/api_service.dart' as _i5;
import 'package:mialamobile/models/bank.dart' as _i3;
import 'package:mialamobile/models/state.dart' as _i4;
import 'package:mialamobile/providers/auth_provider.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [AuthProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthProvider extends _i1.Mock implements _i2.AuthProvider {
  MockAuthProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isLoading => (super.noSuchMethod(
        Invocation.getter(#isLoading),
        returnValue: false,
      ) as bool);

  @override
  bool get isAuthenticated => (super.noSuchMethod(
        Invocation.getter(#isAuthenticated),
        returnValue: false,
      ) as bool);

  @override
  List<_i3.Bank> get banks => (super.noSuchMethod(
        Invocation.getter(#banks),
        returnValue: <_i3.Bank>[],
      ) as List<_i3.Bank>);

  @override
  bool get isLoadingBanks => (super.noSuchMethod(
        Invocation.getter(#isLoadingBanks),
        returnValue: false,
      ) as bool);

  @override
  List<_i4.State> get states => (super.noSuchMethod(
        Invocation.getter(#states),
        returnValue: <_i4.State>[],
      ) as List<_i4.State>);

  @override
  bool get isLoadingStates => (super.noSuchMethod(
        Invocation.getter(#isLoadingStates),
        returnValue: false,
      ) as bool);

  @override
  set apiService(_i5.ApiService? service) => super.noSuchMethod(
        Invocation.setter(
          #apiService,
          service,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i6.Future<bool> checkAuthentication() => (super.noSuchMethod(
        Invocation.method(
          #checkAuthentication,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<bool> login(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [
            email,
            password,
          ],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<Map<String, dynamic>> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<void> clearAuthenticationForPendingApproval() =>
      (super.noSuchMethod(
        Invocation.method(
          #clearAuthenticationForPendingApproval,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<Map<String, dynamic>> verifyBankAccount({
    required String? accountNumber,
    required String? bankCode,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyBankAccount,
          [],
          {
            #accountNumber: accountNumber,
            #bankCode: bankCode,
          },
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<Map<String, dynamic>> fetchBanks() => (super.noSuchMethod(
        Invocation.method(
          #fetchBanks,
          [],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<Map<String, dynamic>> fetchStates() => (super.noSuchMethod(
        Invocation.method(
          #fetchStates,
          [],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<Map<String, dynamic>> forgotPassword({required String? email}) =>
      (super.noSuchMethod(
        Invocation.method(
          #forgotPassword,
          [],
          {#email: email},
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<Map<String, dynamic>> verifyOtpForForgotPassword({
    required String? email,
    required String? otp,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyOtpForForgotPassword,
          [],
          {
            #email: email,
            #otp: otp,
          },
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<Map<String, dynamic>> resetPassword({
    required String? token,
    required String? newPassword,
    String? email,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #resetPassword,
          [],
          {
            #token: token,
            #newPassword: newPassword,
            #email: email,
          },
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<Map<String, dynamic>> signup({
    required String? firstName,
    required String? lastName,
    required String? email,
    required String? password,
    required String? phone,
    required String? accountNumber,
    required String? accountName,
    required String? bankName,
    required String? state,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #signup,
          [],
          {
            #firstName: firstName,
            #lastName: lastName,
            #email: email,
            #password: password,
            #phone: phone,
            #accountNumber: accountNumber,
            #accountName: accountName,
            #bankName: bankName,
            #state: state,
          },
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  void addListener(_i7.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i7.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
